{"name": "fuzzing-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@jiaminghi/data-view": "^2.10.0", "@kjgl77/datav-vue3": "^1.7.3", "aos": "^2.3.4", "axios": "^1.7.7", "element-plus": "^2.8.0", "font-awesome": "^4.7.0", "gsap": "^3.12.5", "js-cookie": "^3.0.5", "qs": "^6.13.0", "tsparticles": "^3.7.1", "tsparticles-engine": "^2.12.0", "vue": "^3.4.35", "vue-router": "^4.4.3", "vuex": "^4.1.0"}, "devDependencies": {"@types/qs": "^6.9.17", "@vitejs/plugin-vue": "^5.1.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "sass-embedded": "^1.77.8", "tailwindcss": "^3.4.17", "vite": "^5.4.0", "vite-plugin-vue": "^0.0.1"}}
<template>
  <el-aside
    v-if="showHeaderAndSidebar && !loading"
    :width="isCollapsed ? '64px' : '250px'"
    class="sidebar"
  >
    <el-menu
      :default-active="$route.path"
      router
      :collapse="isCollapsed"
      class="el-menu-vertical"
      text-color="#a0b7d6"
      active-text-color="#409EFF"
    >
      <!-- <el-menu-item index="/dashboard">
        <el-icon><DataAnalysis /></el-icon>
        <span>仪表盘</span>
      </el-menu-item> -->
      <el-menu-item index="/upload">
        <el-icon><Upload /></el-icon>
        <span>数据管理</span>
      </el-menu-item>
      <el-menu-item index="/rules">
        <el-icon><Document /></el-icon>
        <span>规则信息</span>
      </el-menu-item>
      <el-menu-item index="/test-cases">
        <el-icon><Monitor /></el-icon>
        <span>测试用例</span>
      </el-menu-item>
      <el-menu-item index="/vulnerability">
        <el-icon><Warning /></el-icon> <!-- Use 'Warning' as icon -->
        <span>漏洞检测</span>
      </el-menu-item>
      <el-menu-item v-if="isAdmin" index="/admin">
        <el-icon><Setting /></el-icon>
        <span>管理员</span>
      </el-menu-item>
    </el-menu>
    <el-button class="toggle-collapse-btn" @click="toggleCollapse" type="text">
      <el-icon>
        <Fold v-if="!isCollapsed" />
        <Expand v-if="isCollapsed" />
      </el-icon>
    </el-button>
  </el-aside>
</template>

<script>
import { ElIcon, ElMenu, ElMenuItem } from 'element-plus';
import {
  DataAnalysis,
  Monitor,
  Setting,
  Upload,
  Fold,
  Expand,
  Document,
  Warning,  // Use 'Warning' instead of 'Bug'
} from '@element-plus/icons-vue';

export default {
  components: {
    ElMenu,
    ElMenuItem,
    ElIcon,
    DataAnalysis,
    Upload,
    Monitor,
    Setting,
    Fold,
    Expand,
    Document,
    Warning,  // Adjusted the imported icons
  },
  props: {
    showHeaderAndSidebar: Boolean,
    loading: Boolean,
    isCollapsed: Boolean,
    isAdmin: Boolean,
  },
  methods: {
    toggleCollapse() {
      this.$emit('toggle-collapse');
    },
  },
};
</script>

<style scoped>
.sidebar {
  padding-top: 24px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 93.5vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.05);
  border-right: 1px solid rgba(229, 231, 235, 0.3);
}

.el-menu-vertical {
  border-right: none;
  background: transparent;
}

.el-menu-item {
  font-size: 15px;
  margin-bottom: 8px;
  margin-left: 16px;
  margin-right: 16px;
  height: 48px;
  width: auto;
  color: #6b7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.el-menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: transparent;
  transition: all 0.3s ease;
  border-radius: 0 2px 2px 0;
}

.el-menu-item:hover {
  color: #4ED9F5;
  background: rgba(78, 217, 245, 0.08);
  transform: translateX(2px);
}

.el-menu-item:hover::before {
  background: rgba(78, 217, 245, 0.3);
}

.el-menu-item.is-active {
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.15) 0%, rgba(59, 200, 230, 0.15) 100%);
  color: #4ED9F5;
  border: 1px solid rgba(78, 217, 245, 0.2);
  box-shadow: 0 2px 8px rgba(78, 217, 245, 0.15);
  font-weight: 600;
}

.el-menu-item.is-active::before {
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
}

.el-menu-item i {
  color: #9ca3af;
  transition: all 0.3s ease;
  font-size: 18px;
}

.el-menu-item:hover i {
  color: #4ED9F5;
  transform: scale(1.1);
}

.el-menu-item.is-active i {
  color: #4ED9F5;
  transform: scale(1.1);
}

.toggle-collapse-btn {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  color: #9ca3af;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  font-size: 20px;
  width: 40px;
  height: 40px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-collapse-btn:hover {
  color: #4ED9F5;
  background: rgba(78, 217, 245, 0.1);
  border-color: rgba(78, 217, 245, 0.3);
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 217, 245, 0.2);
}

.toggle-collapse-btn:active {
  transform: translateX(-50%) translateY(0);
}

/* 折叠状态样式 */
.sidebar[style*="width: 64px"] .el-menu-item {
  margin-left: 8px;
  margin-right: 8px;
  justify-content: center;
}

.sidebar[style*="width: 64px"] .el-menu-item::before {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    padding-top: 16px;
  }

  .el-menu-item {
    font-size: 14px;
    height: 44px;
    margin-bottom: 6px;
  }

  .toggle-collapse-btn {
    bottom: 16px;
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
}
</style>

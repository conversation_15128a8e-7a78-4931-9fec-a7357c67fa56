# 智能车载系统安全卫士 - 技术栈文档

## 📋 项目概述

**项目名称**: 智能车载系统安全卫士 (Fuzzing Platform)  
**项目类型**: 前端 Web 应用  
**主要功能**: 车载网络安全检测、模糊测试、漏洞分析  

---

## 🏗️ 核心技术栈

### 前端框架
- **Vue.js 3.4.35** - 渐进式 JavaScript 框架
  - 使用 Composition API 和 Options API 混合开发
  - 支持 TypeScript (部分组件使用 `<script lang="ts" setup>`)

### 构建工具
- **Vite 5.4.0** - 下一代前端构建工具
  - 快速的开发服务器和构建
  - 支持热模块替换 (HMR)
  - ES 模块原生支持

### 路由管理
- **Vue Router 4.4.3** - Vue.js 官方路由管理器
  - 使用 `createWebHistory` 模式
  - 支持路由懒加载和动态路由

### 状态管理
- **Vuex 4.1.0** - Vue.js 官方状态管理库
  - 集中式状态管理
  - 支持模块化状态管理

---

## 🎨 UI 框架与样式

### UI 组件库
- **Element Plus 2.8.0** - Vue 3 企业级 UI 组件库
  - 完整的组件生态系统
  - 支持主题定制和暗色模式
  - 国际化支持

### CSS 框架
- **Tailwind CSS 3.4.17** - 实用优先的 CSS 框架
  - 原子化 CSS 类
  - 响应式设计支持
  - 自定义配置和主题

### CSS 预处理器
- **Sass (SCSS)** - CSS 预处理器
  - 使用 `sass-embedded 1.77.8`
  - 支持变量、嵌套、混入等特性

### PostCSS 插件
- **PostCSS 8.4.49** - CSS 后处理器
- **Autoprefixer 10.4.20** - 自动添加浏览器前缀

---

## 🔧 开发工具与插件

### Vite 插件
- **@vitejs/plugin-vue 5.1.2** - Vue 单文件组件支持
- **vite-plugin-vue 0.0.1** - 额外的 Vue 支持

### 图标库
- **Font Awesome** - 图标字体库
  - `@fortawesome/fontawesome-free 6.7.2`
  - `@fortawesome/vue-fontawesome 3.0.8`
  - `font-awesome 4.7.0` (经典版本)

### 动画库
- **GSAP 3.12.5** - 高性能动画库
- **AOS 2.3.4** - 滚动动画库
- **tsparticles 3.7.1** - 粒子效果库

---

## 📊 数据可视化

### 图表库
- **@jiaminghi/data-view 2.10.0** - 数据可视化组件
- **@kjgl77/datav-vue3 1.7.3** - Vue 3 数据可视化组件

---

## 🌐 网络请求与工具

### HTTP 客户端
- **Axios 1.7.7** - Promise 基础的 HTTP 客户端
  - 请求/响应拦截器
  - 自动 JSON 数据转换
  - 请求和响应数据转换

### 工具库
- **js-cookie 3.0.5** - JavaScript Cookie 操作库
- **qs 6.13.0** - 查询字符串解析和序列化
- **lodash** - JavaScript 实用工具库

---

## 🎯 开发配置

### 包管理器
- **npm/pnpm** - 支持多种包管理器
- **package-lock.json** 和 **pnpm-lock.yaml** 并存

### 代码规范
- **TypeScript 支持** - 部分组件使用 TypeScript
- **ES6+ 语法** - 使用现代 JavaScript 特性

### 环境配置
- **开发环境**: `npm run dev` (Vite 开发服务器)
- **生产构建**: `npm run build` (Vite 构建)
- **预览**: `npm run preview` (构建预览)

---

## 📁 项目结构

```
src/
├── assets/          # 静态资源
│   ├── fonts/       # 自定义字体文件
│   └── background.jpg
├── components/      # 可复用组件
│   ├── Header.vue
│   ├── Sidebar.vue
│   ├── testcases/   # 测试用例相关组件
│   └── upload/      # 上传相关组件
├── router/          # 路由配置
├── store/           # Vuex 状态管理
├── styles/          # 样式文件
├── views/           # 页面组件
└── main.js          # 应用入口
```

---

## 🚀 特色功能

### 主题定制
- 自定义主色调: `#4ED9F5` (青蓝色)
- 支持暗色模式
- Element Plus 主题变量覆盖

### 响应式设计
- 移动端适配
- 断点响应式布局
- 自适应侧边栏

### 用户体验
- 骨架屏加载效果
- 平滑滚动动画
- 光标跟随效果
- 粒子背景效果

### 安全特性
- JWT Token 认证
- 请求拦截器自动添加认证头
- 角色权限管理 (Admin/User)

---

## 📦 依赖版本总览

| 技术栈 | 版本 | 用途 |
|--------|------|------|
| Vue.js | 3.4.35 | 核心框架 |
| Vite | 5.4.0 | 构建工具 |
| Element Plus | 2.8.0 | UI 组件库 |
| Tailwind CSS | 3.4.17 | CSS 框架 |
| Vue Router | 4.4.3 | 路由管理 |
| Vuex | 4.1.0 | 状态管理 |
| Axios | 1.7.7 | HTTP 客户端 |
| GSAP | 3.12.5 | 动画库 |

---

## 🔮 技术特点

1. **现代化技术栈**: 使用最新的 Vue 3 + Vite 技术栈
2. **组件化开发**: 高度模块化的组件设计
3. **类型安全**: 部分使用 TypeScript 提升代码质量
4. **性能优化**: Vite 构建工具提供快速的开发体验
5. **用户体验**: 丰富的动画效果和交互设计
6. **企业级**: Element Plus 提供完整的企业级组件支持

---

*文档生成时间: 2025年1月*

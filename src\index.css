@tailwind base;
@tailwind components;
@tailwind utilities;
@font-face {
    font-family: 'melete';
    src: url('./assets/fonts/Melete.otf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Shrikhand';
    src: url('./assets/fonts/Shrikhand.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'PlaywriteCU-Regular';
    src: url('./assets/fonts/PlaywriteCU-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'BowlbyOne-Regular';
    src: url('./assets/fonts/BowlbyOne-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

h2 {
    color: #374151;
    margin-bottom: 24px;
    font-size: 1.75em;
    font-weight: 600;
    position: relative;
    padding-bottom: 16px;
    text-align: left;
    letter-spacing: -0.025em;
}

h2::after {
    position: absolute;
    bottom: 0;
    left: 0;
    content: '';
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
    border-radius: 2px;
    transition: width 0.3s ease;
}

h2:hover::after {
    width: 80px;
}

/* 全局动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* 动画类 */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 2s infinite;
}

/* 悬停效果 */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* 渐变背景 */
.gradient-bg-primary {
    background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
}

.gradient-bg-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-accent {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 毛玻璃效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 阴影效果 */
.shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.shadow-medium {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.shadow-strong {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.shadow-colored {
    box-shadow: 0 4px 16px rgba(78, 217, 245, 0.3);
}

/* 响应式文字大小 */
@media (max-width: 640px) {
    h2 {
        font-size: 1.5em;
        margin-bottom: 20px;
        padding-bottom: 12px;
    }
}
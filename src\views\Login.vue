<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center relative overflow-hidden">
    <!-- 动态背景粒子 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="floating-particles"></div>
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
    </div>

    <!-- 主要内容容器 -->
    <div class="w-full max-w-md z-10 px-4 animate-fade-in-up">
      <!-- Logo 区域 -->
      <div class="text-center mb-8">
        <div class="logo-container mb-4">
          <font-awesome-icon icon="fa-shield-alt" class="logo-icon" />
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2 tracking-wide">
          智能车载系统安全卫士
        </h1>
        <p class="text-gray-500 text-sm">大模型赋能的车载系统漏洞检测平台</p>
      </div>

      <!-- 登录卡片 -->
      <div class="login-card">
        <!-- 切换标签 -->
        <div class="flex justify-center mb-8">
          <div class="tab-container">
            <button
              @click="activeTab = 'login'"
              :class="['tab-button tab-left', activeTab === 'login' ? 'tab-active' : 'tab-inactive']"
            >
              登录
            </button>
            <button
              @click="goToRegister()"
              :class="['tab-button tab-right', activeTab === 'register' ? 'tab-active' : 'tab-inactive']"
            >
              注册
            </button>
          </div>
        </div>

        <el-form :model="form" :rules="rules" ref="loginForm" class="space-y-6">
          <el-form-item prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入用户名"
              class="custom-input"
              size="large"
            >
              <template #prefix>
                <el-icon class="input-icon"><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              class="custom-input"
              size="large"
              show-password
            >
              <template #prefix>
                <el-icon class="input-icon"><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <!-- 记住密码和忘记密码 -->
          <div class="flex justify-between items-center mb-6">
            <el-checkbox v-model="rememberMe" class="remember-checkbox">
              记住密码
            </el-checkbox>
            <a href="#" class="forgot-password">忘记密码？</a>
          </div>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              @click="handleLogin"
              :loading="loginLoading"
              size="large"
            >
              {{ loginLoading ? '登录中...' : '登 录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 分割线 -->
        <div class="divider">
          <span class="divider-text">或者使用以下方式登录</span>
        </div>

        <!-- 第三方登录 -->
        <div class="social-login">
          <button class="social-btn wechat-btn" @click="handleSocialLogin('wechat')">
            <svg class="social-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18z"/>
              <path d="M15.308 9.537c-1.315 0-2.417.363-3.308 1.003-.89.64-1.336 1.503-1.336 2.59 0 1.086.446 1.949 1.336 2.589.891.64 1.993 1.003 3.308 1.003.276 0 .543-.027.811-.05l1.903 1.114a.326.326 0 0 0 .167.054c.16 0 .29-.132.29-.295 0-.072-.029-.143-.048-.213l-.39-1.48a.59.59 0 0 1 .213-.665c1.832-1.347 3.002-3.338 3.002-5.55 0-4.054-3.891-7.342-8.691-7.342z"/>
            </svg>
            微信登录
          </button>

          <button class="social-btn phone-btn" @click="handleSocialLogin('phone')">
            <svg class="social-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
            </svg>
            手机登录
          </button>

          <button class="social-btn github-btn" @click="handleSocialLogin('github')">
            <svg class="social-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            GitHub
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Lock, User } from '@element-plus/icons-vue';
import axios from "axios";
import Cookies from 'js-cookie';
import qs from 'qs';

export default {
  components: {
    User,
    Lock,
  },
  data() {
    return {
      activeTab: 'login',
      loginLoading: false,
      rememberMe: false,
      form: {
        username: '',
        password: '',
      },
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loginLoading = true;

          axios.post('/api/login',
            qs.stringify({
              username: this.form.username,
              password: this.form.password
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
              }
            }
          )
          .then(response => {
            if (response.data.msg === "success") {
              this.$message.success('登录成功');
              const user = {
                username: this.form.username,
              };
              this.$store.commit('setUser', user);
              const token = response.data.data;

              // 根据记住密码选项设置过期时间
              const expires = this.rememberMe ? 30 : 7;
              Cookies.set('access_token', token, { expires });

              setTimeout(() => {
                this.$router.push('/upload');
              }, 500);
            } else {
              this.$message.error('登录失败，请检查用户名和密码');
            }
          })
          .catch(error => {
            this.$message.error(error.response?.data?.errors || '登录失败');
          })
          .finally(() => {
            this.loginLoading = false;
          });
        }
      });
    },

    handleSocialLogin(type) {
      this.$message.info(`${type === 'wechat' ? '微信' : type === 'phone' ? '手机号' : 'GitHub'}登录功能开发中...`);
    },

    goToRegister() {
      this.$router.push('/register');
    },
  },
};
</script>

<style scoped>
/* 动画效果 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes gradient-shift {
  0%, 100% { transform: translateX(-50%) translateY(-50%) rotate(0deg); }
  50% { transform: translateX(-50%) translateY(-50%) rotate(180deg); }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

/* 背景效果 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: gradient-shift 8s ease-in-out infinite;
}

.orb-1 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.orb-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

/* Logo 样式 */
.logo-container {
  display: inline-block;
  animation: float 3s ease-in-out infinite;
}

.logo-icon {
  font-size: 3rem;
  color: #4ED9F5;
  filter: drop-shadow(0 4px 8px rgba(78, 217, 245, 0.3));
}

/* 登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 标签切换 */
.tab-container {
  display: flex;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(10px);
}

.tab-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.tab-active {
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(78, 217, 245, 0.4);
}

.tab-inactive {
  background: transparent;
  color: #6b7280;
}

.tab-inactive:hover {
  background: rgba(255, 255, 255, 0.8);
  color: #374151;
}

/* 输入框样式 */
.custom-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(229, 231, 235, 0.8);
  border-radius: 16px;
  height: 56px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(78, 217, 245, 0.5);
  background: rgba(255, 255, 255, 0.95);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #4ED9F5;
  box-shadow: 0 0 0 4px rgba(78, 217, 245, 0.1);
  background: rgba(255, 255, 255, 1);
}

.custom-input :deep(.el-input__inner) {
  color: #374151;
  font-size: 16px;
  font-weight: 500;
}

.input-icon {
  color: #9ca3af;
  font-size: 18px;
  transition: color 0.3s ease;
}

.custom-input :deep(.el-input__wrapper.is-focus) .input-icon {
  color: #4ED9F5;
}

/* 记住密码和忘记密码 */
.remember-checkbox :deep(.el-checkbox__label) {
  color: #6b7280;
  font-size: 14px;
}

.remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #4ED9F5;
  border-color: #4ED9F5;
}

.forgot-password {
  color: #4ED9F5;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #3bc8e6;
  text-decoration: underline;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 56px;
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(78, 217, 245, 0.4);
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(78, 217, 245, 0.5);
  background: linear-gradient(135deg, #3bc8e6 0%, #2ab7d5 100%);
}

.login-button:active {
  transform: translateY(0);
}

/* 分割线 */
.divider {
  position: relative;
  text-align: center;
  margin: 2rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, #e5e7eb, transparent);
}

.divider-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 1rem;
  color: #9ca3af;
  font-size: 14px;
  font-weight: 500;
}

/* 第三方登录 */
.social-login {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.social-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid rgba(229, 231, 235, 0.8);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.wechat-btn:hover {
  border-color: #07c160;
  color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.phone-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.github-btn:hover {
  border-color: #1f2937;
  color: #1f2937;
  background: rgba(31, 41, 55, 0.05);
}

.social-icon {
  width: 18px;
  height: 18px;
}

/* 错误信息样式 */
:deep(.el-form-item__error) {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 1.5rem;
    margin: 1rem;
  }

  .social-login {
    flex-direction: column;
  }

  .social-btn {
    justify-content: center;
  }
}
</style>
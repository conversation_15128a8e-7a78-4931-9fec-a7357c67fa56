<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 text-gray-800 overflow-x-hidden" @mousemove="handleMouseMove">
    <!-- 跟随鼠标的蓝色圆点 -->
    <div class="mouse-follower" :style="mouseFollowerStyle"></div>

    <!-- Hero Section -->
    <section
      class="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100"
    >
      <div class="absolute inset-0 z-0">
        <img
          src="https://ai-public.mastergo.com/ai/img_res/5066ff6207b2fe945fd04e7852b7d616.jpg"
          alt="Background"
          class="w-full h-full object-cover object-center opacity-80"
        />
      </div>
      <div class="relative z-10 text-center px-4">
        <!-- <img src="https://ai-public.mastergo.com/ai/img_res/9db5799e872b53e151928a0dc4c312df.jpg" alt="Logo" class="w-32 h-32 mx-auto mb-6"> -->
        <h1 class="text-7xl font-bold mb-10 bg-gradient-to-r from-cyan-400 via-blue-500 to-teal-500 bg-clip-text text-transparent animate-pulse tracking-tight">
          智能车载系统安全卫士
        </h1>
        <p class="text-2xl mb-8 text-gray-700 font-medium leading-relaxed tracking-wide">大模型赋能，精准检测，为您的车载网络保驾护航</p>
        <!-- 在 template 中 -->
        <el-button
          type="primary"
          class="custom-button text-white font-bold py-4 px-16 transition-all duration-300 text-lg relative overflow-hidden"
          @click="goToLogin"
        >
          立即体验
        </el-button>

        <!-- <button @click="goToLogin" class="bg-[#4ED9F5] text-gray-900 px-8 py-3 rounded-full text-lg font-semibold hover:bg-[#3bc8e4] transition-colors duration-300 !rounded-button whitespace-nowrap get-start-button">立即体验</button> -->
      </div>
      <div
        class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce"
      >
        <!-- <i class="fas fa-angle-down text-[#4ED9F5] text-3xl"></i> -->
        <font-awesome-icon
          @click="scrollToCallToAction"
          icon="fa-solid fa-angle-down"
          class="text-[#4ED9F5] text-3xl hover:text-[#3bc8e6] transition-colors duration-300 cursor-pointer animate-bounce"
        />
      </div>
    </section>

    <!-- 图片与下方内容的衔接 -->
    <div class="wave-transition">
      <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="wave-svg">
        <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" class="wave-fill"></path>
        <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" class="wave-fill"></path>
        <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" class="wave-fill"></path>
      </svg>
    </div>

    <!-- Platform Advantages -->
    <section class="py-20 px-4 bg-white relative">
      <!-- 动态背景效果 -->
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
      <h1 class="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-[#4ED9F5] to-[#3bc8e6] bg-clip-text text-transparent tracking-tight">
        平台优势
      </h1>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <div
          v-for="(advantage, index) in advantages"
          :key="index"
          class="advantage-card"
        >
          <div class="card-content">
            <div class="icon-wrapper">
              <i :class="advantage.icon + ' card-icon'"></i>
            </div>
            <h3 class="card-title">
              {{ advantage.title }}
            </h3>
            <p class="card-description">
              {{ advantage.shortDescription }}
            </p>

            <!-- 特性标签 -->
            <div class="feature-tags">
              <span v-for="(feature, idx) in advantage.features" :key="idx"
                    class="feature-tag">
                {{ feature }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Call to Action -->
    <section ref="callToAction" class="py-20 px-4 text-center bg-gradient-to-b from-white to-blue-50">
      <h1 class="text-4xl font-bold mb-6 text-[#4ED9F5]">
        开始使用智能车载系统安全卫士
      </h1>
      <p class="text-xl mb-8 text-gray-700">
        立即体验大模型赋能的模糊测试，提升您的车载网络安全
      </p>
      <button
        @click="goToLogin"
        class="bg-[#4ED9F5] text-white px-12 py-4 rounded-full text-xl font-semibold hover:bg-[#3EAEC2] transition-all duration-300 transform hover:scale-105 !rounded-button whitespace-nowrap shadow-lg hover:shadow-xl"
      >
        开始使用
      </button>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-8 px-4 border-t border-gray-200">
      <div
        class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center"
      >
        <div class="text-gray-600 mb-4 md:mb-0">
          © 2025 智能车载系统卫士. 保留所有权利.
        </div>
        <div class="flex space-x-4">
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >隐私政策</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >使用条款</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >联系我们</a
          >
        </div>
      </div>
    </footer>



  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, reactive } from "vue";
import router from "../router";

const advantages = [
  {
    icon: "fas fa-brain",
    title: "智能分析",
    shortDescription: "利用大模型技术，智能识别潜在威胁，提高检测准确率",
    description: "基于先进的大模型技术，深度学习车载网络行为模式，智能识别异常流量和潜在威胁，显著提升安全检测的准确率和效率。通过机器学习算法持续优化检测模型，实现对未知威胁的预测性防护。",
    features: ["AI 驱动分析", "深度学习算法", "智能威胁识别", "自动化分析"],
    details: {
      accuracy: "99.5%",
      speed: "毫秒级响应",
      coverage: "全协议支持"
    }
  },
  {
    icon: "fas fa-shield-alt",
    title: "全面防护",
    shortDescription: "覆盖车载网络各个层面，为您的车辆提供全方位安全保障",
    description: "构建多层次、全方位的车载网络安全防护体系，从物理层到应用层全面覆盖，为您的智能车辆提供企业级安全保障。集成入侵检测、防火墙、访问控制等多种安全机制，形成立体化防护网络。",
    features: ["多层次防护", "实时监控预警", "全网络覆盖", "主动防御机制"],
    details: {
      layers: "7层防护",
      monitoring: "24/7监控",
      response: "秒级响应"
    }
  },
  {
    icon: "fas fa-tachometer-alt",
    title: "高效检测",
    shortDescription: "快速扫描和分析，及时发现并报告安全隐患",
    description: "采用高性能检测引擎，实现快速扫描和实时分析，能够在最短时间内发现安全隐患并生成详细的安全报告。支持并行处理和分布式检测，大幅提升检测效率和覆盖范围。",
    features: ["高速扫描引擎", "实时威胁检测", "精准漏洞定位", "智能报告生成"],
    details: {
      scanSpeed: "10Gbps",
      detection: "实时检测",
      reporting: "自动生成"
    }
  },
];

const chartContainer = ref<HTMLElement | null>(null);

// 鼠标跟随效果
const mouseFollowerStyle = ref({
  left: '0px',
  top: '0px',
  transform: 'translate(-50%, -50%)',
  opacity: '0'
});

// 优化的鼠标跟随处理函数
const handleMouseMove = (event: MouseEvent) => {
  requestAnimationFrame(() => {
    mouseFollowerStyle.value = {
      left: `${event.clientX}px`,
      top: `${event.clientY}px`,
      transform: 'translate(-50%, -50%)',
      opacity: '1'
    };
  });
};

// 鼠标离开页面时隐藏圆点
const handleMouseLeave = () => {
  mouseFollowerStyle.value.opacity = '0';
};

// 鼠标进入页面时显示圆点
const handleMouseEnter = () => {
  mouseFollowerStyle.value.opacity = '1';
};

// 获取详细信息标签
const getDetailLabel = (key: string) => {
  const labels: { [key: string]: string } = {
    accuracy: '检测精度',
    speed: '响应速度',
    coverage: '协议覆盖',
    layers: '防护层级',
    monitoring: '监控时间',
    response: '响应时间',
    scanSpeed: '扫描速度',
    detection: '检测模式',
    reporting: '报告生成'
  };
  return labels[key] || key;
};

const scrollToCallToAction = () => {
  const element = document.querySelector(".py-20");
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};

const goToLogin = () => {
  router.push("/login");
};

onMounted(() => {
  // 添加鼠标事件监听器
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseleave', handleMouseLeave);
  document.addEventListener('mouseenter', handleMouseEnter);

  // 组件卸载时清理事件监听器
  return () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseleave', handleMouseLeave);
    document.removeEventListener('mouseenter', handleMouseEnter);
  };
});
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap");

/* 全局字体优化 */
* {
  font-family: "Inter", "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga" 1, "kern" 1;
}

body {
  font-family: "Inter", "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1.6;
  font-weight: 400;
}

/* 标题字体优化 */
h1, h2, h3, h4, h5, h6 {
  font-family: "Inter", "Noto Sans SC", sans-serif;
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* 数据字体 */
.detail-value, .feature-tag {
  font-family: "JetBrains Mono", "Inter", "Noto Sans SC", monospace;
}

/* 行高优化 */
.line-height-relaxed {
  line-height: 1.7;
}

.swiper-pagination-bullet {
  background-color: #4ED9F5;
}

.swiper-pagination-bullet-active {
  background-color: #3EAEC2;
}

/* 自定义输入框样式 */
input[type="text"],
input[type="email"],
input[type="password"] {
  @apply bg-white border-gray-300 text-gray-700 focus:border-[#4ED9F5] focus:ring-[#4ED9F5];
}

/* 去除number类型输入框的默认箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
}

::-webkit-scrollbar-thumb {
  background: #4ED9F5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3EAEC2;
}

/* 鼠标跟随效果 */
.mouse-follower {
  position: fixed;
  width: 24px;
  height: 24px;
  background: radial-gradient(circle,
    rgba(78, 217, 245, 0.9) 0%,
    rgba(78, 217, 245, 0.6) 30%,
    rgba(78, 217, 245, 0.3) 60%,
    transparent 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: opacity 0.3s ease;
  box-shadow:
    0 0 15px rgba(78, 217, 245, 0.8),
    0 0 30px rgba(78, 217, 245, 0.4),
    0 0 45px rgba(78, 217, 245, 0.2);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 0 15px rgba(78, 217, 245, 0.8),
      0 0 30px rgba(78, 217, 245, 0.4),
      0 0 45px rgba(78, 217, 245, 0.2);
  }
  100% {
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow:
      0 0 20px rgba(78, 217, 245, 1),
      0 0 40px rgba(78, 217, 245, 0.6),
      0 0 60px rgba(78, 217, 245, 0.3);
  }
}

/* 在移动设备上隐藏鼠标跟随效果 */
@media (max-width: 768px) {
  .mouse-follower {
    display: none;
  }
}

/* 波浪过渡效果 */
.wave-transition {
  position: relative;
  height: 120px;
  overflow: hidden;
  line-height: 0;
}

.wave-svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 120px;
}

.wave-fill {
  fill: #ffffff;
}

/* 动态背景形状 */
.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.shape {
  position: absolute;
  opacity: 0.1;
  animation: float-shapes 20s infinite linear;
}

.shape-1 {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, #4ED9F5, #3bc8e6);
  border-radius: 50%;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #3bc8e6, #2ab7d5);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  top: 60%;
  left: 80%;
  animation-delay: 5s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #4ED9F5, #6ee0f7);
  border-radius: 50%;
  top: 30%;
  left: 70%;
  animation-delay: 10s;
}

.shape-4 {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #8ee7f9, #4ED9F5);
  border-radius: 50%;
  top: 80%;
  left: 20%;
  animation-delay: 15s;
}

.shape-5 {
  width: 70px;
  height: 70px;
  background: linear-gradient(45deg, #aeeefb, #8ee7f9);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  top: 20%;
  left: 40%;
  animation-delay: 8s;
}

@keyframes float-shapes {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  100% {
    transform: translateY(0px) rotate(360deg);
  }
}

/* 优化的卡片样式 */
.advantage-card {
  position: relative;
  height: 380px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid rgba(78, 217, 245, 0.12);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  transform: translateY(0) scale(1);
  will-change: transform, box-shadow;
}

.advantage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.06) 0%, rgba(59, 200, 230, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.advantage-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(78, 217, 245, 0.15),
    0 4px 20px rgba(0, 0, 0, 0.08);
  border-color: rgba(78, 217, 245, 0.25);
}

.advantage-card:hover::before {
  opacity: 1;
}

.card-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 2rem;
  text-align: center;
}

/* 图标包装器增强 */
.icon-wrapper {
  position: relative;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图标背景 */
.icon-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.1), rgba(78, 217, 245, 0.05));
  border-radius: 50%;
  transition: all 0.4s ease;
  z-index: 1;
}

/* 图标脉冲效果 */
.icon-pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(78, 217, 245, 0.3);
  border-radius: 50%;
  opacity: 0;
  transform: scale(1);
  animation: iconPulseAnimation 2s ease-in-out infinite;
  z-index: 0;
}

@keyframes iconPulseAnimation {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.advantage-card:hover .icon-wrapper {
  transform: scale(1.15) translateY(-4px) rotateY(5deg);
}

.advantage-card:hover .icon-background {
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.2), rgba(78, 217, 245, 0.1));
  transform: scale(1.1);
}

.advantage-card:hover .icon-pulse {
  animation-duration: 1s;
}

.card-icon {
  font-size: 4rem;
  color: #4ED9F5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 8px rgba(78, 217, 245, 0.2));
  position: relative;
  z-index: 2;
}

.advantage-card:hover .card-icon {
  color: #3bc8e6;
  filter: drop-shadow(0 4px 16px rgba(78, 217, 245, 0.4));
}

.card-title {
  font-family: "Inter", "Noto Sans SC", sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  line-height: 1.3;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #4ED9F5, #3bc8e6);
  transition: width 0.3s ease;
  transform: translateX(-50%);
}

.advantage-card:hover .card-title {
  color: #4ED9F5;
  transform: translateY(-1px);
}

.advantage-card:hover .card-title::after {
  width: 60%;
}

.card-description {
  font-family: "Inter", "Noto Sans SC", sans-serif;
  font-size: 1rem;
  font-weight: 400;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
  letter-spacing: -0.01em;
  opacity: 0.9;
}

.advantage-card:hover .card-description {
  color: #4b5563;
  opacity: 1;
}

/* 特性标签样式 */
.feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-top: auto;
}

.feature-tag {
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.08) 0%, rgba(78, 217, 245, 0.04) 100%);
  color: #4ED9F5;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid rgba(78, 217, 245, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  letter-spacing: 0.02em;
  font-family: "JetBrains Mono", "Inter", monospace;
  position: relative;
  overflow: hidden;
}

.feature-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.4s ease;
}

.advantage-card:hover .feature-tag {
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
  color: white;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(78, 217, 245, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.advantage-card:hover .feature-tag::before {
  left: 100%;
}

/* 为每个标签添加延迟动画 */
.feature-tag:nth-child(1) { transition-delay: 0.05s; }
.feature-tag:nth-child(2) { transition-delay: 0.1s; }
.feature-tag:nth-child(3) { transition-delay: 0.15s; }
.feature-tag:nth-child(4) { transition-delay: 0.2s; }

/* 响应式优化 */
@media (max-width: 768px) {
  .advantage-card {
    height: 320px;
  }

  .card-content {
    padding: 2rem 1.5rem;
  }

  .card-icon {
    font-size: 3rem;
  }

  .card-title {
    font-size: 1.25rem;
  }

  .card-description {
    font-size: 0.9rem;
  }

  .feature-tag {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
}

.custom-button {
  position: relative;
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%) !important;
  border: 1px solid transparent !important;
  border-radius: 50px !important;
  height: 56px !important;
  color: #ffffff !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(78, 217, 245, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.custom-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.custom-button:hover {
  background: linear-gradient(135deg, #3bc8e6 0%, #2ab7d5 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(78, 217, 245, 0.5) !important;
}

.custom-button:hover::before {
  left: 100%;
}

.custom-button:active {
  transform: translateY(0) !important;
}

/* 卡片进入动画 */
@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.advantage-card {
  animation: cardSlideIn 0.6s ease-out forwards;
}

.advantage-card:nth-child(1) { animation-delay: 0.1s; }
.advantage-card:nth-child(2) { animation-delay: 0.2s; }
.advantage-card:nth-child(3) { animation-delay: 0.3s; }

/* 卡片点击效果 */
.advantage-card:active {
  transform: translateY(-6px) scale(1.01);
  transition: all 0.1s ease;
}

/* 简化的卡片动画 */
.advantage-card {
  animation: cardSlideIn 0.6s ease-out forwards;
}

/* 添加微妙的悬停反馈 */
.advantage-card:hover {
  animation: none; /* 移除其他动画 */
}

/* 优化的焦点状态 */
.advantage-card:focus {
  outline: 2px solid rgba(78, 217, 245, 0.5);
  outline-offset: 4px;
}

/* 添加平滑的过渡效果 */
.advantage-card * {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 优化移动端体验 */
@media (hover: none) {
  .advantage-card:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  }

  .advantage-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}
</style>

